{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://plandex.ai/schemas/model-pack-inline.schema.json", "title": "Inline Model Pack Config", "description": "Inline model pack config for plan model settings or default model settings", "allOf": [{"$ref": "./model-pack-roles.schema.json"}], "properties": {"$schema": true, "name": true, "description": true, "localProvider": true, "planner": true, "coder": true, "architect": true, "summarizer": true, "builder": true, "wholeFileBuilder": true, "names": true, "commitMessages": true, "autoContinue": true}, "additionalProperties": false}