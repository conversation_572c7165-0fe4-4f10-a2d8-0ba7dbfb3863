{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://plandex.ai/schemas/model-pack-roles.schema.json", "title": "Model Pack Roles", "type": "object", "description": "Config for a model pack's roles", "definitions": {"roleRef": {"description": "Can be a string like 'openai/o3-high' or an object with model config if you want to defined role properties like temperature/topP, or fallbacks like 'largeContextFallback', 'largeOutputFallback', 'errorFallback', 'strongModel'", "oneOf": [{"type": "string", "minLength": 1}, {"$ref": "./model-role-config.schema.json"}]}}, "properties": {"$schema": {"type": "string"}, "localProvider": {"description": "The local provider for the model pack", "$ref": "./definitions/local-providers.schema.json"}, "planner": {"description": "This is the 'main' role that replies to prompts and makes plans.", "$ref": "#/definitions/roleRef"}, "coder": {"description": "This role writes code to implement each step of the plan made by the 'planner' role during the planning stage.\n\nInstruction-following is important for this role as it needs to follow specific formatting rules.", "$ref": "#/definitions/roleRef"}, "architect": {"description": "When auto-context is enabled, this role makes a high-level plan using the project map, then determines what context to provide for the 'planner' role.", "$ref": "#/definitions/roleRef"}, "summarizer": {"description": "Summarizes conversations to stay under the limit set in the model's 'defaultMaxConvoTokens'.", "$ref": "#/definitions/roleRef"}, "builder": {"description": "Builds the proposed changes described by the `planner` role into pending file updates.", "$ref": "#/definitions/roleRef"}, "wholeFileBuilder": {"description": "Builds the proposed changes described by the `planner` role into pending file updates by writing the entire file. Used as a fallback if more targeted edits fail.\n\nThis role is optional. It falls back to the `builder` role if not set.", "$ref": "#/definitions/roleRef"}, "names": {"description": "Gives automatically-generated names to plans and context.", "$ref": "#/definitions/roleRef"}, "commitMessages": {"description": "Automatically generates commit messages for a set of pending updates.", "$ref": "#/definitions/roleRef"}, "autoContinue": {"description": "Determines whether a plan is finished or should automatically continue based on the previous response.", "$ref": "#/definitions/roleRef"}}, "required": ["planner", "summarizer", "builder", "names", "commitMessages", "autoContinue"]}