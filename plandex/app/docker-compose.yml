services:
  plandex-postgres:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_PASSWORD: plandex
      POSTGRES_USER: plandex
      POSTGRES_DB: plandex
    ports:
      - "5432:5432"
    volumes:
      - plandex-db:/var/lib/postgresql/data
    networks:
      - plandex-network
  plandex-server:
    image: plandexai/plandex-server:latest
    # build:
    #   context: .
    #   dockerfile: server/Dockerfile
    volumes:
      - plandex-files:/plandex-server
    ports:
      - "8099:8099"
      - "4000:4000"
    environment:
      DATABASE_URL: "************************************************/plandex?sslmode=disable"
      GOENV: development
      LOCAL_MODE: 1
      PLANDEX_BASE_DIR: /plandex-server
      OLLAMA_BASE_URL: http://host.docker.internal:11434
      
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - plandex-network
    depends_on:
      - plandex-postgres
    command: [ "/bin/sh", "-c", "/scripts/wait-for-it.sh plandex-postgres:5432 -- ./plandex-server" ]

networks:
  plandex-network:
    driver: bridge

volumes:
  plandex-db:
  plandex-files: