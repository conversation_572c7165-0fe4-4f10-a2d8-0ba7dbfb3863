Based on conversation below with ChatGPT, I want you to fix the race condition as described with a retry mechanism similar to deadlock handling. I want it to be absolutely fucking impossible to have a race condition no matter the fuck what.

Convo with chatgpt:

locks.go:

```
package db

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/lib/pq"
)

const lockHeartbeatInterval = 700 * time.Millisecond
const lockHeartbeatTimeout = 4 * time.Second

// distributed locking to ensure only one user can write to a plan repo at a time
// multiple readers are allowed, but read locks block writes
// write lock is exclusive (blocks both reads and writes)

type LockRepoParams struct {
	OrgId       string
	UserId      string
	PlanId      string
	Branch      string
	Scope       LockScope
	PlanBuildId string
	Ctx         context.Context
	CancelFn    context.CancelFunc
}

func LockRepo(params LockRepoParams) (string, error) {
	return lockRepo(params, 0)
}

func lockRepo(params LockRepoParams, numRetry int) (string, error) {
	log.Println("locking repo")
	// spew.Dump(params)

	orgId := params.OrgId
	userId := params.UserId
	planId := params.PlanId
	branch := params.Branch
	scope := params.Scope
	planBuildId := params.PlanBuildId
	ctx := params.Ctx
	cancelFn := params.CancelFn

	tx, err := Conn.BeginTxx(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return "", fmt.Errorf("error starting transaction: %v", err)
	}

	// Ensure that rollback is attempted in case of failure
	defer func() {
		if err != nil {
			if rbErr := tx.Rollback(); rbErr != nil {
				log.Printf("transaction rollback error: %v\n", rbErr)
			} else {
				log.Println("transaction rolled back")
			}
		}
	}()

	query := "SELECT id, org_id, user_id, plan_id, plan_build_id, scope, branch, created_at FROM repo_locks WHERE plan_id = $1 FOR UPDATE"
	queryArgs := []interface{}{planId}

	var locks []*repoLock

	fn := func() error {
		rows, err := tx.Query(query, queryArgs...)
		if err != nil {
			if pqErr, ok := err.(*pq.Error); ok && (pqErr.Code == "40001" || pqErr.Code == "40P01") {
				// return concurrency errors directly for retries
				return err
			}

			return fmt.Errorf("error getting repo locks: %v", err)
		}

		defer rows.Close()

		var expiredLockIds []string

		now := time.Now()
		for rows.Next() {
			var lock repoLock
			if err := rows.Scan(&lock.Id, &lock.OrgId, &lock.UserId, &lock.PlanId, &lock.PlanBuildId, &lock.Scope, &lock.Branch, &lock.CreatedAt); err != nil {
				return fmt.Errorf("error scanning repo lock: %v", err)
			}

			// ensure heartbeat hasn't timed out
			if now.Sub(lock.LastHeartbeatAt) < lockHeartbeatTimeout {
				locks = append(locks, &lock)
			} else {
				expiredLockIds = append(expiredLockIds, lock.Id)
			}
		}

		if len(expiredLockIds) > 0 {
			query := "DELETE FROM repo_locks WHERE id = ANY($1)"
			_, err := tx.Exec(query, pq.Array(expiredLockIds))
			if err != nil {
				return fmt.Errorf("error removing expired locks: %v", err)
			}
		}

		return nil
	}
	err = fn()
	if err != nil {
		if pqErr, ok := err.(*pq.Error); ok && (pqErr.Code == "40001" || pqErr.Code == "40P01") {
			if numRetry > 10 {
				err = fmt.Errorf("plan is currently being updated by another user")
				return "", err
			}

			log.Printf("Serialization or deadlock error, retrying transaction: %v\n", err)

			wait := time.Duration(numRetry) * 300 * time.Millisecond * time.Duration(rand.Intn(500)*int(time.Millisecond))

			select {
			case <-ctx.Done():
				return "", fmt.Errorf("context finished during retry transaction")
			case <-time.After(wait):
				return lockRepo(params, numRetry+1)
			}
		}

		return "", err
	}

	canAcquire := true
	canRetry := true

	// log.Println("locks:")
	// spew.Dump(locks)

	for _, lock := range locks {
		lockBranch := ""
		if lock.Branch != nil {
			lockBranch = *lock.Branch
		}

		if scope == LockScopeRead {
			canAcquireThisLock := lock.Scope == LockScopeRead && lockBranch == branch
			if !canAcquireThisLock {
				canAcquire = false
			}
		} else if scope == LockScopeWrite {
			canAcquire = false

			// if lock is for the same plan plan and branch, allow parallel writes
			if planId == lock.PlanId && branch == lockBranch {
				canAcquire = true
			}

			if lock.Scope == LockScopeWrite && lockBranch == branch {
				canRetry = false
			}
		} else {
			err = fmt.Errorf("invalid lock scope: %v", scope)
			return "", err
		}
	}

	if !canAcquire {
		log.Println("can't acquire lock. canRetry:", canRetry, "numRetry:", numRetry)

		if canRetry {
			// 10 second timeout
			if numRetry > 20 {
				err = fmt.Errorf("plan is currently being updated by another user")
				return "", err
			}
			time.Sleep(500 * time.Millisecond)
			return lockRepo(params, numRetry+1)
		}
		err = fmt.Errorf("plan is currently being updated by another user")
		return "", err
	}

	// Insert the new lock
	var lockPlanBuildId *string
	if planBuildId != "" {
		lockPlanBuildId = &planBuildId
	}

	var lockBranch *string
	if branch != "" {
		lockBranch = &branch
	}

	newLock := &repoLock{
		OrgId:       orgId,
		UserId:      userId,
		PlanId:      planId,
		PlanBuildId: lockPlanBuildId,
		Scope:       scope,
		Branch:      lockBranch,
	}
	// log.Println("newLock:")
	// spew.Dump(newLock)

	insertQuery := "INSERT INTO repo_locks (org_id, user_id, plan_id, plan_build_id, scope, branch) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id"
	err = tx.QueryRow(
		insertQuery,
		newLock.OrgId,
		newLock.UserId,
		newLock.PlanId,
		newLock.PlanBuildId,
		newLock.Scope,
		newLock.Branch,
	).Scan(&newLock.Id)
	if err != nil {
		return "", fmt.Errorf("error inserting new lock: %v", err)
	}

	// check if git lock file exists
	// remove it if so
	err = gitRemoveIndexLockFileIfExists(getPlanDir(orgId, planId))
	if err != nil {
		return "", fmt.Errorf("error removing lock file: %v", err)
	}

	branches, err := GitListBranches(orgId, planId)
	if err != nil {
		return "", fmt.Errorf("error getting branches: %v", err)
	}

	log.Println("branches:", branches)

	if branch != "" {
		// checkout the branch
		err = gitCheckoutBranch(getPlanDir(orgId, planId), branch)
		if err != nil {
			return "", fmt.Errorf("error checking out branch: %v", err)
		}
	}

	// Commit the transaction
	if err = tx.Commit(); err != nil {
		return "", fmt.Errorf("error committing transaction: %v", err)
	}

	// Start a goroutine to keep the lock alive
	go func() {
		numErrors := 0
		for {
			select {
			case <-ctx.Done():
				// log.Printf("case <-stream.Ctx.Done(): %s\n", newLock.Id)
				err := UnlockRepo(newLock.Id)
				if err != nil {
					log.Printf("Error unlocking repo: %v\n", err)
				}
				return

			default:
				res, err := Conn.Exec("UPDATE repo_locks SET last_heartbeat_at = NOW() WHERE id = $1", newLock.Id)

				if err != nil {
					log.Printf("Error updating repo lock last heartbeat: %v\n", err)
					numErrors++

					if numErrors > 5 {
						log.Printf("Too many errors updating repo lock last heartbeat: %v\n", err)
						cancelFn()
						return
					}
				}

				// check if 0 rows were updated
				rowsAffected, err := res.RowsAffected()
				if err != nil {
					log.Printf("Error getting rows affected: %v\n", err)
					cancelFn()
					return
				}

				if rowsAffected == 0 {
					log.Printf("Lock not found: %s | stopping heartbeat loop\n", newLock.Id)
					return
				}

				time.Sleep(lockHeartbeatInterval)
			}

		}
	}()

	log.Println("repo locked. id:", newLock.Id)

	return newLock.Id, nil
}

func UnlockRepo(id string) error {
	log.Println("unlocking repo:", id)

	query := "DELETE FROM repo_locks WHERE id = $1"
	_, err := Conn.Exec(query, id)
	if err != nil {
		return fmt.Errorf("error removing lock: %v", err)
	}

	log.Println("repo unlocked successfully:", id)

	return nil
}
```

git.go:


```
package db

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/fatih/color"
)

func init() {
	// ensure git is available
	cmd := exec.Command("git", "--version")
	if err := cmd.Run(); err != nil {
		panic(fmt.Errorf("error running git --version: %v", err))
	}
}

func InitGitRepo(orgId, planId string) error {
	dir := getPlanDir(orgId, planId)
	return initGitRepo(dir)
}

func initGitRepo(dir string) error {
	// Set the default branch name to 'main' for the new repository
	res, err := exec.Command("git", "-C", dir, "init", "-b", "main").CombinedOutput()
	if err != nil {
		return fmt.Errorf("error initializing git repository with 'main' as default branch for dir: %s, err: %v, output: %s", dir, err, string(res))
	}

	// Configure user name and email for the repository
	if err := setGitConfig(dir, "user.email", "<EMAIL>"); err != nil {
		return err
	}
	if err := setGitConfig(dir, "user.name", "Plandex"); err != nil {
		return err
	}

	return nil
}

func GitAddAndCommit(orgId, planId, branch, message string) error {
	dir := getPlanDir(orgId, planId)

	err := gitAdd(dir, ".")
	if err != nil {
		return fmt.Errorf("error adding files to git repository for dir: %s, err: %v", dir, err)
	}

	err = gitCommit(dir, message)
	if err != nil {
		return fmt.Errorf("error committing files to git repository for dir: %s, err: %v", dir, err)
	}

	return nil
}

// func GitAddAndAmendCommit(orgId, planId, branch, addMessage string) error {
// 	dir := getPlanDir(orgId, planId)

// 	err := gitAdd(dir, ".")
// 	if err != nil {
// 		return fmt.Errorf("error adding files to git repository for dir: %s, err: %v", dir, err)
// 	}

// 	// Get the latest commit message
// 	_, latestCommitMsg, err := getLatestCommit(dir)
// 	if err != nil {
// 		return fmt.Errorf("error getting latest commit message for dir: %s, err: %v", dir, err)
// 	}

// 	// Amend the latest commit with the new message
// 	message := latestCommitMsg + "\n\n" + addMessage
// 	res, err := exec.Command("git", "-C", dir, "commit", "--amend", "-m", message).CombinedOutput()

// 	if err != nil {
// 		return fmt.Errorf("error amending commit for dir: %s, err: %v, output: %s", dir, err, string(res))
// 	}

// 	return nil
// }

func GitRewindToSha(orgId, planId, branch, sha string) error {
	dir := getPlanDir(orgId, planId)

	err := gitRewindToSha(dir, sha)
	if err != nil {
		return fmt.Errorf("error rewinding git repository for dir: %s, err: %v", dir, err)
	}

	return nil
}

func GetGitCommitHistory(orgId, planId, branch string) (body string, shas []string, err error) {
	dir := getPlanDir(orgId, planId)

	body, shas, err = getGitCommitHistory(dir)
	if err != nil {
		return "", nil, fmt.Errorf("error getting git history for dir: %s, err: %v", dir, err)
	}

	return body, shas, nil
}

func GetLatestCommit(orgId, planId, branch string) (sha, body string, err error) {
	dir := getPlanDir(orgId, planId)

	sha, body, err = getLatestCommit(dir)
	if err != nil {
		return "", "", fmt.Errorf("error getting latest commit for dir: %s, err: %v", dir, err)
	}

	return sha, body, nil
}

func GitListBranches(orgId, planId string) ([]string, error) {
	dir := getPlanDir(orgId, planId)

	var out bytes.Buffer
	cmd := exec.Command("git", "branch", "--format=%(refname:short)")
	cmd.Dir = dir
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("error getting git branches for dir: %s, err: %v", dir, err)
	}

	branches := strings.Split(strings.TrimSpace(out.String()), "\n")

	if len(branches) == 0 || (len(branches) == 1 && branches[0] == "") {
		return []string{"main"}, nil
	}

	return branches, nil
}

func GitCreateBranch(orgId, planId, branch, newBranch string) error {
	dir := getPlanDir(orgId, planId)

	res, err := exec.Command("git", "-C", dir, "checkout", "-b", newBranch).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error creating git branch for dir: %s, err: %v, output: %s", dir, err, string(res))
	}

	return nil
}

func GitDeleteBranch(orgId, planId, branchName string) error {
	dir := getPlanDir(orgId, planId)

	res, err := exec.Command("git", "-C", dir, "branch", "-D", branchName).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error deleting git branch for dir: %s, err: %v, output: %s", dir, err, string(res))
	}

	return nil
}

func GitClearUncommittedChanges(orgId, planId string) error {
	dir := getPlanDir(orgId, planId)

	// Reset staged changes
	res, err := exec.Command("git", "-C", dir, "reset", "--hard").CombinedOutput()
	if err != nil {
		return fmt.Errorf("error resetting staged changes | err: %v, output: %s", err, string(res))
	}

	// Clean untracked files
	res, err = exec.Command("git", "-C", dir, "clean", "-d", "-f").CombinedOutput()
	if err != nil {
		return fmt.Errorf("error cleaning untracked files | err: %v, output: %s", err, string(res))
	}

	return nil
}

// Not used currently but may be good to handle these errors specifically later if locking can't fully prevent them
// func isLockFileError(output string) bool {
// 	return strings.Contains(output, "fatal: Unable to create") && strings.Contains(output, ".git/index.lock': File exists")
// }

func gitCheckoutBranch(repoDir, branch string) error {
	// get current branch and only checkout if it's not the same
	// trying to check out the same branch will result in an error
	var out bytes.Buffer
	cmd := exec.Command("git", "-C", repoDir, "branch", "--show-current")
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("error getting current git branch for dir: %s, err: %v", repoDir, err)
	}

	currentBranch := strings.TrimSpace(out.String())

	log.Println("currentBranch:", currentBranch)

	if currentBranch == branch {
		return nil
	}

	log.Println("checking out branch:", branch)

	res, err := exec.Command("git", "-C", repoDir, "checkout", branch).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error checking out git branch for dir: %s, err: %v, output: %s", repoDir, err, string(res))
	}

	return nil
}

func gitRewindToSha(repoDir, sha string) error {
	res, err := exec.Command("git", "-C", repoDir, "reset", "--hard",
		sha).CombinedOutput()

	if err != nil {
		return fmt.Errorf("error executing git reset for dir: %s, sha: %s, err: %v, output: %s", repoDir, sha, err, string(res))
	}

	return nil
}

func getLatestCommit(dir string) (sha, body string, err error) {
	var out bytes.Buffer
	cmd := exec.Command("git", "log", "--pretty=%h@@|@@%at@@|@@%B@>>>@")
	cmd.Dir = dir
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		return "", "", fmt.Errorf("error getting git history for dir: %s, err: %v",
			dir, err)
	}

	// Process the log output to get it in the desired format.
	history := processGitHistoryOutput(strings.TrimSpace(out.String()))

	first := history[0]

	sha = first[0]
	body = first[1]

	return sha, body, nil
}

func getGitCommitHistory(dir string) (body string, shas []string, err error) {
	var out bytes.Buffer
	cmd := exec.Command("git", "log", "--pretty=%h@@|@@%at@@|@@%B@>>>@")
	cmd.Dir = dir
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		return "", nil, fmt.Errorf("error getting git history for dir: %s, err: %v",
			dir, err)
	}

	// Process the log output to get it in the desired format.
	history := processGitHistoryOutput(strings.TrimSpace(out.String()))

	var output []string
	for _, el := range history {
		shas = append(shas, el[0])
		output = append(output, el[1])
	}

	return strings.Join(output, "\n\n"), shas, nil
}

// processGitHistoryOutput processes the raw output from the git log command and returns a formatted string.
func processGitHistoryOutput(raw string) [][2]string {
	var history [][2]string
	entries := strings.Split(raw, "@>>>@") // Split entries using the custom separator.

	for _, entry := range entries {
		// First clean up any leading/trailing whitespace or newlines from each entry.
		entry = strings.TrimSpace(entry)

		// Now split the cleaned entry into its parts.
		parts := strings.Split(entry, "@@|@@")
		if len(parts) == 3 {
			sha := parts[0]
			timestampStr := parts[1]
			message := strings.TrimSpace(parts[2]) // Trim whitespace from message as well.

			// Extract and format timestamp.
			timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
			if err != nil {
				continue // Skip entries with invalid timestamps.
			}

			dt := time.Unix(timestamp, 0).UTC()
			formattedTs := dt.Format("Mon Jan 2, 2006 | 3:04:05pm MST")

			// Prepare the header with colors.
			headerColor := color.New(color.FgCyan, color.Bold)
			dateColor := color.New(color.FgCyan)

			// Combine sha, formatted timestamp, and message header into one string.
			header := fmt.Sprintf("%s | %s", headerColor.Sprintf("📝 Update %s", sha), dateColor.Sprintf("%s", formattedTs))

			// Combine header and message with a newline only if the message is not empty.
			fullEntry := header
			if message != "" {
				fullEntry += "\n" + message
			}

			history = append(history, [2]string{sha, fullEntry})
		}
	}

	return history
}

func gitAdd(repoDir, path string) error {
	res, err := exec.Command("git", "-C", repoDir, "add", path).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error adding files to git repository for dir: %s, err: %v, output: %s", repoDir, err, string(res))
	}

	return nil
}

func gitCommit(repoDir, commitMsg string) error {
	res, err := exec.Command("git", "-C", repoDir, "commit", "-m", commitMsg).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error committing files to git repository for dir: %s, err: %v, output: %s", repoDir, err, string(res))
	}

	return nil
}

func gitRemoveIndexLockFileIfExists(repoDir string) error {
	// Remove the lock file if it exists
	lockFilePath := filepath.Join(repoDir, ".git", "index.lock")
	_, err := os.Stat(lockFilePath)

	// log.Println("lockFilePath:", lockFilePath)
	// log.Println("exists:", err == nil)

	if err == nil {
		if err := os.Remove(lockFilePath); err != nil {
			if os.IsNotExist(err) {
				return nil
			}

			return fmt.Errorf("error removing lock file: %v", err)
		}
	} else if !os.IsNotExist(err) {
		return fmt.Errorf("error checking lock file: %v", err)
	}

	return nil
}

func setGitConfig(repoDir, key, value string) error {
	res, err := exec.Command("git", "-C", repoDir, "config", key, value).CombinedOutput()
	if err != nil {
		return fmt.Errorf("error setting git config %s to %s for dir: %s, err: %v, output: %s", key, value, repoDir, err, string(res))
	}
	return nil
}
```

somehow a race condition is still possible here even though it shouldn't be. definitely rare but a user reported this -- image attached for ya

ChatGPT
It looks like you're encountering a fatal: unable to write new_index file error while trying to commit files to a Git repository. This issue is typically caused by an existing .git/index.lock file that was not properly removed after an operation, possibly due to a previous Git operation that didn't complete normally or was interrupted.

Here are a few steps you can take to resolve the issue:

Ensure No Git Processes Are Running: Make sure there are no ongoing Git operations that might be holding the lock. You can check and terminate any such processes if necessary.

Remove the Lock File Manually:

Navigate to the .git directory in your repository.
Check for the presence of an index.lock file.
If it exists, manually delete the index.lock file. Ensure that no Git operations are running when you do this.
Check File System Permissions: Verify that your user account has the necessary permissions to write to the files in the .git directory of your repository.

File System Issues: Sometimes, a corrupted file system or hardware issues can also lead to this error. Ensure that the disk is not full or read-only, and run a disk check if necessary.

Retry the Operation: After taking the above steps, try to commit again.

If these steps don't resolve the issue, it could be helpful to review the code surrounding your Git operations, especially how locks are managed in cases of concurrent access or failed operations. Your existing mechanism may need additional handling to ensure that locks are always released properly, even in error conditions.

Also, it’s worth checking the logic in your custom distributed locking system implemented in your locks.go to ensure that it’s effectively preventing race conditions and properly handling retries and rollbacks in case of errors. The issue might be tied to subtle timing issues or edge cases not covered by the current retry and locking logic.

i'm thinking i should be treating this ephemeral weird error kinda like a deadlock and have a retry with some kind of backoff maybe??
ChatGPT
Adding retry logic with exponential backoff is a good strategy to handle transient errors like this, particularly in distributed systems where race conditions or temporary file locking issues might occur. This method can help in overcoming temporary glitches without failing the operation immediately, especially when the error is likely to resolve itself in a short period, such as a locked file or a network glitch.

Here's a general approach you can adopt:

Retry with Exponential Backoff: Implement a retry mechanism that waits for an increasing amount of time before retrying the failed operation. The delay prevents immediate retries, which could exacerbate the problem, especially under high load or when many instances are competing for the same resources.

Limit the Number of Retries: Set a reasonable limit on the number of retries to avoid endless loops. After the maximum number of retries, log the error and potentially alert for manual intervention.

Logging: Log each retry attempt with its waiting time and reason for failure. This logging will be crucial for diagnosing issues in production environments.

Handle Specific Errors: If possible, identify and handle specific errors that should trigger a retry, such as the "unable to write new_index file" error. You might want to handle other errors differently.

Here’s a simple example in pseudocode to implement exponential backoff:

pseudo
Copy code
maxRetries := 5
retryInterval := 100 // milliseconds

for attempt := 0; attempt < maxRetries; attempt++ {
    err := performGitOperation()
    if err == nil {
        break
    } else if isRetryableError(err) {
        log("Retry attempt", attempt + 1, "failed. Waiting", retryInterval, "ms before retry.")
        sleep(retryInterval)
        retryInterval *= 2 // Double the interval for the next retry
    } else {
        log("Non-retryable error encountered:", err)
        break
    }
}

if err != nil {
    log("Operation failed after", maxRetries, "attempts.")
}
Implementation Notes:
Function performGitOperation would encapsulate the Git commands that might fail due to the lock issue.
Function isRetryableError would check if the error should trigger a retry (e.g., checking the error message for specific text).
Sleep and Logging: Handling actual sleep and logging based on your environment (consider appropriate logging framework or system sleep functions).
This pattern ensures that temporary problems can be mitigated by giving the system a brief period to stabilize (e.g., releasing locks) before retrying, thereby improving robustness against transient faults.

please output the error message i paste in as a screenshot in *text*
ChatGPT
Since I can't directly view images, I can't extract text from the screenshot you uploaded. To help you further with the error message, you could either type it out or use an OCR (Optical Character Recognition) tool to convert the screenshot to text and then paste it here.

don't you already have it in your context? ocr it dude
ChatGPT
Sorry for the oversight! Let me extract the text from your screenshot. Here's the error message from your screenshot:

vbnet
Copy code
src/modules/CrudProducts.astro | src/services/products.ts
Server error
  * Stream error
  * Failed to commit
  * Error committing files to git repository for dir
    /plandex-server/orgs/d17f7c27-9314-4b63-9d79-8a09825f92b/plans/7738457c-f419-4f33-81ff-056949a3c6d8, err
    * Exit status 128, output
      * Fatal: unable to write new_index file
It seems like you're facing a Git-related issue where it's unable to write to the new_index file. This problem could be due to an existing lock file, permission issues, or a corrupt repository state. As previously suggested, you might want to manually check for .git/index.lock file and ensure your directory permissions are correctly set to allow write operations.

--

fix this problem please so I never have to worry about it again and it will be impossible for anyone to get this error. but also be conservative and make sure you change the minimum necessary to accomplish this and don't introduce any new bugs or race conditions.
