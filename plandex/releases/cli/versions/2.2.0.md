This is a big release that is mainly focused on Plandex's model provider and model config system. It significantly increases model provider flexibility, makes custom model configuration much easier, reduces costs on Cloud, and adds built-in support for Ollama.

## 🔌  Model provider flexibility for BYO key mode

- Now when using Plandex in BYO key mode (either Cloud or self-hosted), you can easily use Plandex with a wide range of built-in model providers.

- Apart from OpenRouter and the OpenAI API (which were already built-in), built-in providers now include:
  - Anthropic API
  - Google AI Studio
  - Google Vertex AI
  - Azure OpenAI
  - AWS Bedrock
  - DeepSeek API
  - Perplexity API
  - Ollama (for local models—see below for details)

- See the new [model provider docs](https://docs.plandex.ai/models/model-providers) for more details.

![plandex-model-providers](https://github.com/plandex-ai/plandex/blob/main/releases/images/cli/2.2.0/new-providers.gif)

## 🛟  Provider fallback

- When API keys/credentials are provided for multiple providers for a model, Plandex will fail over to the last valid provider if the first one fails. This is especially useful when using a direct provider (like OpenAI or Anthropic) alongside OpenRouter. If the direct call fails, Plandex will fall back to OpenRouter, which has its *own* internal fallback system across multiple providers. You get the best of both worlds: direct access by default, with no additional cost or latency, plus multi-layered resilience in case of stability issues.

## 💰  5.5% price reduction for Plandex Cloud Integrated Models mode

- Thanks to the new model provider system described above, Plandex Cloud with Integrated Models mode can now make direct provider calls under the hood rather than defaulting to OpenRouter, which allows us to avoid OpenRouter's 5.5% surcharge on model calls and pass the savings on to you. OpenRouter is still used as a fallback for added resilience.

## ⚙️  JSON-based model config with IDE support

- Plandex now supports JSON-based model config, which make it much easier to try out different models, different settings, and to use custom models, providers, and model packs.

- The new JSON model config system integrates cleanly with your IDE or editor. When you first edit model settings, Plandex will prompt you to set a preferred editor, and the JSON config file will be opened in that editor.

- Model config files use JSON schemas, which allows for autocomplete, validation, and inline documentation/type hints in most IDEs and editors.

- Check out the new docs for [model settings](https://docs.plandex.ai/models/model-settings) and [custom models](https://docs.plandex.ai/models/custom-models) for full details.

### `set-model` and `set-model default` commands

- `set-model` has been simplified to work with the new system. If run without arguments, you'll be prompted to either select a built-in or custom model pack, or to directly edit the current plan's model config inline in JSON. You can also pass it a model pack name (`set-model daily-driver`) or jump straight to the JSON settings with `set-model --json`.

- `set-model default` works the same way, but allows you to configure the default model settings for new plans.

![plandex-model-settings-json](https://github.com/plandex-ai/plandex/blob/main/releases/images/cli/2.2.0/model-settings-json.gif)


### `models custom` command

- `models custom` is a new all-in-one command for managing custom providers, models, and model packs in one place. It replaces the `models add`, `models delete`, `model-packs create`, `model-packs update`, and `model-packs delete` commands.

- The first time you run it, if you haven't already configured any custom models or model packs, an example config file will be created to get you started. If you *do* already have custom models or model packs configured, the config file will be populated with those models and model packs.

![plandex-custom-models-json](https://github.com/plandex-ai/plandex/blob/main/releases/images/cli/2.2.0/custom-models-json.gif)

### `models` and `models default` commands

- The `models` and `models default` commands now show simplified output by default, with a new `--all` flag to show all properties.

- These commands also now show all fallback models (for large context, large output, errors, etc.) for each role, including multiple levels of fallback, which previously weren't always included in the output.

## 🦙  Built-in Ollama support

- Plandex now offers built-in support for Ollama, which makes it much easier to use local models. Check out the new [Ollama quickstart](https://docs.plandex.ai/models/ollama) for details.

![plandex-ollama](https://github.com/plandex-ai/plandex/blob/main/releases/images/cli/2.2.0/ollama.gif)

## 📖  Built-in models and models packs

- The docs now include all built-in [models](https://docs.plandex.ai/models/built-in/built-in-models) and [model packs](https://docs.plandex.ai/models/built-in/built-in-packs), making it easier to see what's available, and the settings for each model/model pack.

## 🧠  New built-in models

- `mistral/devstral-small`, with both OpenRouter and Ollama providers.

- The Qwen 3 series of models, from 8B to 235B, available with `cloud` variants for OpenRouter and `local` variants for Ollama.

- Distilled local versions of `deepseek/r1`, from 8b to 70b, available with Ollama provider.

## 🎛️  New model packs

- The `gemini-exp` model pack has been removed, and in its place there's now a new `gemini-planner` model pack, which uses Gemini 2.5 Pro for planning and context selection, and the default models for other roles, as well as a new `google` model pack, which uses either Gemini 2.5 Pro or Gemini 2.5 Flash for all roles.

- A new `o3-planner` model pack has been added, which uses OpenAI o3-medium for planning and context selection, and the default models for other roles.

## 🔄  Other built-in model updates

- Built-in Gemini 2.5 Pro and Gemini 2.5 Flash models now use the latest model identifiers (replacing old 2.5 Pro Preview and 2.5 Flash Preview identifiers)

- The `gemini-preview` model pack has been removed, and a new `gemini-planner` model pack has been added, which uses Gemini 2.5 Pro for planning and context selection, and the default models for other roles.

- OpenAI o3 models have had their pricing drastically reduced when using Plandex Cloud with Integrated Models mode—input tokens now cost $2/M, output $8/M, an 80% reduction.

- The `deepseek/r1` model has been updated to use the latest model identifier (`deepseek/deepseek-r1-0528`) on OpenRouter.

## 🐞  Bug fixes

- Fixed a file mapping bug for TypeScript files that caused directly exported symbols like `export const foo = 'bar'` to be omitted from map files. Also improved TypeScript mapping support for some other constructs like `declare global`, `namespace`, and `enum` blocks, and improved handling of arrow functions. Thanks to @mnahkies for the [PR](https://github.com/plandex-ai/plandex/pull/239) identifying this.

## 🔧  Other changes

- `plandex checkout` now has a `--yes/-y` flag to auto-confirm creating a new branch if it doesn't exist, so the command can be used for scripting with no user interaction.

- `plandex tell`, `plandex continue`, and `plandex build` all now support a `--skip-menu` flag to skip the interactive menu that appears when the response finishes and changes are pending. There's also a new `skip-changes-menu` config setting that can be set to `true` to skip this menu by default.