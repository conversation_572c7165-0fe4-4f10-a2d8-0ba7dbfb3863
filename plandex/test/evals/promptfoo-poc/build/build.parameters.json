{"type": "object", "properties": {"comments": {"type": "array", "items": {"type": "object", "properties": {"txt": {"type": "string"}, "reference": {"type": "boolean"}}, "required": ["txt", "reference"]}}, "filePath": {"type": "string"}, "changes": {"type": "array", "items": {"type": "object", "properties": {"summary": {"type": "string"}, "hasChange": {"type": "boolean"}, "old": {"type": "object", "properties": {"entireFile": {"type": "boolean"}, "startLineString": {"type": "string"}, "endLineString": {"type": "string"}}, "required": ["startLineString", "endLineString"]}, "startLineIncludedReasoning": {"type": "string"}, "startLineIncluded": {"type": "boolean"}, "endLineIncludedReasoning": {"type": "string"}, "endLineIncluded": {"type": "boolean"}, "new": {"type": "string"}}, "required": ["summary", "hasChange", "old", "startLineIncludedReasoning", "startLineIncluded", "endLineIncludedReasoning", "endLineIncluded", "new"]}}}, "required": ["comments", "filePath", "changes"]}