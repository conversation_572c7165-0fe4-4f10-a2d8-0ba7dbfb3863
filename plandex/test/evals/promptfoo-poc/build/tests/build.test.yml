- description: "Check Build with Line numbers"
  vars:
    preBuildState: file://assets/shared/pre_build.go
    changes: file://assets/build/changes.md
    filePath: parse.go
    postBuildState: file://assets/build/post_build.go
  assert:
    - type: is-json
    - type: is-valid-openai-tools-call
    - type: javascript
      value: |
        var args = JSON.parse(output[0].function.arguments)
        return ( 
          args.changes.length > 0 &&
          args.changes.some(
            change => change.hasChange && 
                      change.new.includes("var contextRmCmd = &cobra.Command{")
          )
        )
