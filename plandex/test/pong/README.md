# Building Pong in C/OpenGL with Plandex

This directory is a result of running `plandex tell` with the `prompt.txt` file in the root of the repository, then using Plandex to work through a number of compiler errors.

[Here's a video of the whole process.](https://www.youtube.com/watch?v=0ULjQx25S_Y)

## Building and running

The project was designed to be run on a mac.

First install dependencies with:

```bash
./install_dependencies.sh
```

Then build the project with:

```bash
make
```

And run it with:

```bash
./pong
```