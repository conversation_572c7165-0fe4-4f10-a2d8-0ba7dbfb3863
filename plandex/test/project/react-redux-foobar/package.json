{"name": "react-redux-foobar", "version": "1.0.0", "description": "A realistic React/Redux/TypeScript project", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@reduxjs/toolkit": "^1.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-redux": "^8.0.0", "react-scripts": "5.0.0", "typescript": "^4.5.5"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^13.2.0", "@testing-library/user-event": "^14.2.1", "@types/jest": "^27.4.0", "@types/node": "^17.0.21", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}