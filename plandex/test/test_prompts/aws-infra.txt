I want to deploy api infrastructure with the following components:          
                                                                              
These should all be done with the AWS CDK and tied together in              
infra/lib/main.ts. The infra project will need simple, minimalistic package.json and tsconfig.json files in `infra`.                                                          
                                                                            
Please pay close attention to detail and ensure every setting is correct,   
secure, and production ready. At the same time, do not include any          
nonessential attributes or complexity. Keep it as simple as it can be while 
still being correct.                                                        
                                                                            
• A basic VPC                                                               
• Two public subnets that allow outgoing requests                           
• Two private subnets for the database                                      
• An RDS Aurora postgresql database with smallest possible size--just a single
instance with no replicas for now. it should run in the private subnets.    
• An ECR repository where container images can be stored that are loaded by 
the fargate cluster                                                         
• A fargate ECS cluster that runs an image from the ECR repository. just one
replica in the cluster for now                                              
• The fargate cluster should have access to the RDS database. the cluster   
should run in public subnets that can make outgoing requests to the         
internet.                                                                   
• Replicas within the fargate cluster should be able to make requests to    
each other internally- The fargate cluster and RDS should use a secrets     
manager secret for the database credentials.                                
• I need an EFS shared file system that all the fargate cluster containers  
are connected to                                                            
• The fargate containers also need to send emails via SES... please include 
any necessary resources                                                     
                                                                            
please use your best judgement and select reasonable values for all of      
these. the db password should be generated by secrets manager. use defaults 
and best practices.
